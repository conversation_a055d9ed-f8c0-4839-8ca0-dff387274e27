"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@clerk/nextjs"
import { TopNavigation } from "@/components/top-navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { Upload, BookOpen, FileText, CheckCircle, AlertCircle } from "lucide-react"
import { uploadPDFBook, formatFileSize } from "@/lib/file-upload"
import { supabase } from "@/lib/supabase"

interface BookFormData {
  title: string
  author: string
  description: string
  genre: string
  isbn: string
  publicationYear: string
  totalPages: string
  tags: string
}

export default function AddBookPage() {
  const [uploadMethod, setUploadMethod] = useState<"pdf" | "manual">("pdf")
  const [isUploading, setIsUploading] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState<string>("")
  const [formData, setFormData] = useState<BookFormData>({
    title: "",
    author: "",
    description: "",
    genre: "",
    isbn: "",
    publicationYear: "",
    totalPages: "",
    tags: ""
  })

  const router = useRouter()
  const { user } = useUser()
  const { toast } = useToast()

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (file.type !== "application/pdf") {
      toast({
        title: "Invalid file type",
        description: "Please upload a PDF file.",
        variant: "destructive"
      })
      return
    }

    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to upload books.",
        variant: "destructive"
      })
      return
    }

    setIsUploading(true)
    setUploadProgress("Uploading file...")
    setUploadedFile(file)

    try {
      const result = await uploadPDFBook(file, user.id)

      if (result.success && result.metadata) {
        setUploadProgress("File uploaded successfully!")

        // Auto-fill form with extracted metadata
        setFormData(prev => ({
          ...prev,
          title: result.metadata?.title || file.name.replace('.pdf', ''),
          author: result.metadata?.author || "",
          totalPages: result.metadata?.pageCount?.toString() || ""
        }))

        toast({
          title: "PDF uploaded successfully",
          description: `File: ${file.name} (${formatFileSize(file.size)})`,
        })
      } else {
        throw new Error(result.error || "Upload failed")
      }
    } catch (error) {
      console.error("Upload error:", error)
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      })
      setUploadedFile(null)
    } finally {
      setIsUploading(false)
    }
  }

  const handleInputChange = (field: keyof BookFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to add books.",
        variant: "destructive"
      })
      return
    }

    // Validate required fields
    if (!formData.title || !formData.author || !formData.totalPages) {
      toast({
        title: "Missing required fields",
        description: "Please fill in title, author, and total pages.",
        variant: "destructive"
      })
      return
    }

    setIsUploading(true)
    setUploadProgress("Saving book...")

    try {
      const bookData = {
        user_id: user.id,
        title: formData.title,
        author: formData.author,
        description: formData.description || null,
        genre: formData.genre || null,
        isbn: formData.isbn || null,
        publication_year: formData.publicationYear ? parseInt(formData.publicationYear) : null,
        total_pages: parseInt(formData.totalPages),
        current_page: 0,
        reading_status: 'not_started' as const,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : [],
        pdf_url: uploadedFile ? `${user.id}/pdfs/${uploadedFile.name}` : null,
        file_size: uploadedFile?.size || null
      }

      const { data, error } = await supabase
        .from('books')
        .insert([bookData])
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      toast({
        title: "Book added successfully",
        description: `"${formData.title}" has been added to your library.`,
      })

      // Redirect to the book detail page
      router.push(`/book/${data.id}`)
    } catch (error) {
      console.error("Save error:", error)
      toast({
        title: "Failed to save book",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      })
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <TopNavigation />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Add New Book</h1>
            <p className="text-gray-600 dark:text-gray-300">Upload a PDF or manually enter book details</p>
          </div>

          {/* Upload Method Selection */}
          <div className="grid grid-cols-2 gap-4 mb-8">
            <Button
              variant={uploadMethod === "pdf" ? "default" : "outline"}
              onClick={() => setUploadMethod("pdf")}
              className="h-20 flex-col space-y-2"
            >
              <Upload className="w-6 h-6" />
              <span>Upload PDF</span>
            </Button>
            <Button
              variant={uploadMethod === "manual" ? "default" : "outline"}
              onClick={() => setUploadMethod("manual")}
              className="h-20 flex-col space-y-2"
            >
              <FileText className="w-6 h-6" />
              <span>Manual Entry</span>
            </Button>
          </div>

          {/* PDF Upload */}
          {uploadMethod === "pdf" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Upload className="w-5 h-5" />
                  <span>Upload PDF Book</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  uploadedFile
                    ? 'border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/20'
                    : 'border-gray-300 dark:border-gray-600'
                }`}>
                  {uploadedFile ? (
                    <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                  ) : (
                    <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  )}
                  <div className="space-y-2">
                    {uploadedFile ? (
                      <>
                        <p className="text-lg font-medium text-green-700 dark:text-green-300">
                          PDF uploaded successfully!
                        </p>
                        <p className="text-sm text-green-600 dark:text-green-400">
                          {uploadedFile.name} ({formatFileSize(uploadedFile.size)})
                        </p>
                      </>
                    ) : (
                      <>
                        <p className="text-lg font-medium text-gray-900 dark:text-white">
                          Drop your PDF here or click to browse
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Supports PDF files up to 50MB</p>
                      </>
                    )}
                  </div>
                  <input type="file" accept=".pdf" onChange={handleFileUpload} className="hidden" id="pdf-upload" />
                  <Label htmlFor="pdf-upload" className="cursor-pointer">
                    <Button className="mt-4" disabled={isUploading} variant={uploadedFile ? "outline" : "default"}>
                      {isUploading ? "Uploading..." : uploadedFile ? "Change File" : "Choose File"}
                    </Button>
                  </Label>
                </div>

                {isUploading && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <div className="flex items-center space-x-3">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                      <span className="text-blue-600 dark:text-blue-400">
                        {uploadProgress || "Processing PDF and extracting metadata..."}
                      </span>
                    </div>
                  </div>
                )}

                {/* Auto-filled metadata display */}
                {uploadedFile && formData.title && (
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-green-700 dark:text-green-300">Metadata extracted</h4>
                        <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                          Book details have been automatically filled below. You can edit them if needed.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="tags">Tags (optional)</Label>
                    <Input
                      id="tags"
                      placeholder="fiction, classic, literature"
                      className="mt-1"
                      value={formData.tags}
                      onChange={(e) => handleInputChange('tags', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="genre">Genre</Label>
                    <Input
                      id="genre"
                      placeholder="Fiction, Non-fiction, etc."
                      className="mt-1"
                      value={formData.genre}
                      onChange={(e) => handleInputChange('genre', e.target.value)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Manual Entry */}
          {uploadMethod === "manual" && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="w-5 h-5" />
                  <span>Manual Book Entry</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Book Title *</Label>
                    <Input id="title" placeholder="Enter book title" className="mt-1" required />
                  </div>
                  <div>
                    <Label htmlFor="author">Author *</Label>
                    <Input id="author" placeholder="Enter author name" className="mt-1" required />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="pages">Total Pages *</Label>
                    <Input id="pages" type="number" placeholder="300" className="mt-1" required />
                  </div>
                  <div>
                    <Label htmlFor="isbn">ISBN (optional)</Label>
                    <Input id="isbn" placeholder="978-0-123456-78-9" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="year">Publication Year</Label>
                    <Input id="year" type="number" placeholder="2023" className="mt-1" />
                  </div>
                </div>

                <div>
                  <Label htmlFor="description">Description (optional)</Label>
                  <Textarea id="description" placeholder="Brief description of the book..." className="mt-1" rows={3} />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="tags-manual">Tags</Label>
                    <Input id="tags-manual" placeholder="fiction, classic, literature" className="mt-1" />
                  </div>
                  <div>
                    <Label htmlFor="category-manual">Category</Label>
                    <Input id="category-manual" placeholder="Fiction, Non-fiction, etc." className="mt-1" />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between mt-8">
            <Button variant="outline" size="lg">
              Cancel
            </Button>
            <Button size="lg" className="bg-indigo-600 hover:bg-indigo-700">
              Add Book
            </Button>
          </div>
        </div>
      </main>
    </div>
  )
}
