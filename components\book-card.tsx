import Image from "next/image"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { BookOpen, Calendar } from "lucide-react"
import { format } from "date-fns"

interface Book {
  id: number
  title: string
  author: string
  currentPage: number
  totalPages: number
  coverUrl: string
  lastRead: string
}

interface BookCardProps {
  book: Book
}

export function BookCard({ book }: BookCardProps) {
  const progress = (book.currentPage / book.totalPages) * 100
  const pagesLeft = book.totalPages - book.currentPage

  return (
    <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 hover:shadow-md transition-shadow">
      <div className="flex space-x-4">
        {/* Book Cover */}
        <div className="flex-shrink-0">
          <Image
            src={book.coverUrl || "/placeholder.svg"}
            alt={book.title}
            width={60}
            height={80}
            className="rounded-lg object-cover shadow-sm"
          />
        </div>

        {/* Book Info */}
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-gray-900 dark:text-white truncate">{book.title}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-300 truncate">{book.author}</p>

          {/* Progress */}
          <div className="mt-2 space-y-2">
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <span>
                Page {book.currentPage} of {book.totalPages}
              </span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Last Read */}
          <div className="flex items-center mt-2 text-xs text-gray-500 dark:text-gray-400">
            <Calendar className="w-3 h-3 mr-1" />
            <span>Last read: {format(new Date(book.lastRead), 'MMM d, yyyy')}</span>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between mt-3">
            <span className="text-xs text-gray-500 dark:text-gray-400">{pagesLeft} pages left</span>
            <div className="flex space-x-2">
              <Button size="sm" variant="outline" asChild>
                <Link href={`/book/${book.id}`}>
                  <BookOpen className="w-3 h-3 mr-1" />
                  Details
                </Link>
              </Button>
              <Button size="sm" asChild>
                <Link href={`/book/${book.id}/read`}>Continue</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
