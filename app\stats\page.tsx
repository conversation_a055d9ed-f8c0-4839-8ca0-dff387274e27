import { TopNavigation } from "@/components/top-navigation"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { BookOpen, Clock, Target, TrendingUp, Calendar, Award } from "lucide-react"
import Link from "next/link"

export default function StatsPage() {
  // Mock data for demonstration
  const yearlyStats = {
    booksRead: 24,
    pagesRead: 8547,
    hoursRead: 342,
    averageRating: 4.2,
    longestStreak: 45,
    currentStreak: 7
  }

  const monthlyData = [
    { month: "Jan", books: 3, pages: 890 },
    { month: "Feb", books: 2, pages: 654 },
    { month: "Mar", books: 4, pages: 1200 },
    { month: "Apr", books: 3, pages: 987 },
    { month: "May", books: 2, pages: 543 },
    { month: "Jun", books: 3, pages: 876 }
  ]

  const topGenres = [
    { genre: "Fiction", count: 12, percentage: 50 },
    { genre: "Non-Fiction", count: 6, percentage: 25 },
    { genre: "Science Fiction", count: 4, percentage: 17 },
    { genre: "Biography", count: 2, percentage: 8 }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      
      <main className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Reading Statistics</h1>
          <p className="text-gray-600">Track your reading progress and achievements</p>
        </div>

        {/* Yearly Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Books Read</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{yearlyStats.booksRead}</div>
              <p className="text-xs text-muted-foreground">This year</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pages Read</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{yearlyStats.pagesRead.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">This year</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Reading Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{yearlyStats.hoursRead}h</div>
              <p className="text-xs text-muted-foreground">This year</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Streak</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{yearlyStats.currentStreak}</div>
              <p className="text-xs text-muted-foreground">Days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Longest Streak</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{yearlyStats.longestStreak}</div>
              <p className="text-xs text-muted-foreground">Days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{yearlyStats.averageRating}</div>
              <p className="text-xs text-muted-foreground">Out of 5 stars</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Monthly Progress */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Progress</CardTitle>
              <CardDescription>Books and pages read each month</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyData.map((month) => (
                  <div key={month.month} className="flex items-center justify-between">
                    <div className="font-medium">{month.month}</div>
                    <div className="text-sm text-gray-600">
                      {month.books} books • {month.pages} pages
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Genres */}
          <Card>
            <CardHeader>
              <CardTitle>Favorite Genres</CardTitle>
              <CardDescription>Your most read genres this year</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topGenres.map((genre) => (
                  <div key={genre.genre} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{genre.genre}</div>
                      <div className="text-sm text-gray-600">{genre.count} books</div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-indigo-600 h-2 rounded-full" 
                        style={{ width: `${genre.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <Button asChild>
            <Link href="/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
      </main>
    </div>
  )
}
