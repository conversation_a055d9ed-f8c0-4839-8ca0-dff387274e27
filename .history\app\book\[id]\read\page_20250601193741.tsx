"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@clerk/nextjs"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { PDFReader } from "@/components/pdf-reader"
import { supabase } from "@/lib/supabase"
import {
  Highlighter,
  Eraser,
  Pencil,
  StickyNote,
  BookmarkIcon,
  Sun,
  Moon,
  BookOpen,
  X,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Save,
  ArrowLeft,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

interface Book {
  id: string
  title: string
  author: string
  pdf_url: string | null
  current_page: number
  total_pages: number
  reading_status: string
}

export default function FullScreenReadPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const { user } = useUser()
  const { toast } = useToast()

  const [book, setBook] = useState<Book | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [readingStartTime, setReadingStartTime] = useState<Date | null>(null)

  // Load book data
  useEffect(() => {
    const loadBook = async () => {
      if (!user) return

      try {
        const { data, error } = await supabase
          .from('books')
          .select('*')
          .eq('id', params.id)
          .eq('user_id', user.id)
          .single()

        if (error) {
          throw new Error(error.message)
        }

        if (!data) {
          toast({
            title: "Book not found",
            description: "The requested book could not be found.",
            variant: "destructive"
          })
          router.push('/dashboard')
          return
        }

        setBook(data)
        setCurrentPage(data.current_page || 1)
        setReadingStartTime(new Date())
      } catch (error) {
        console.error('Error loading book:', error)
        toast({
          title: "Error loading book",
          description: "Failed to load book data.",
          variant: "destructive"
        })
        router.push('/dashboard')
      } finally {
        setIsLoading(false)
      }
    }

    loadBook()
  }, [params.id, user, router, toast])

  // Handle page change and update progress
  const handlePageChange = async (page: number) => {
    setCurrentPage(page)

    if (!book || !user) return

    try {
      // Update book progress in database
      await supabase
        .from('books')
        .update({
          current_page: page,
          last_read_date: new Date().toISOString(),
          reading_status: page >= book.total_pages ? 'completed' : 'reading'
        })
        .eq('id', book.id)
        .eq('user_id', user.id)

      // Update local state
      setBook(prev => prev ? { ...prev, current_page: page } : null)
    } catch (error) {
      console.error('Error updating progress:', error)
    }
  }

  // Handle closing the reader
  const handleClose = async () => {
    // Save reading session if we have start time
    if (readingStartTime && book && user) {
      try {
        const endTime = new Date()
        const durationMinutes = Math.round((endTime.getTime() - readingStartTime.getTime()) / 60000)

        if (durationMinutes > 0) {
          await supabase
            .from('reading_sessions')
            .insert({
              user_id: user.id,
              book_id: book.id,
              start_time: readingStartTime.toISOString(),
              end_time: endTime.toISOString(),
              pages_read: Math.max(0, currentPage - (book.current_page || 1)),
              start_page: book.current_page || 1,
              end_page: currentPage,
              reading_duration_minutes: durationMinutes,
              session_date: new Date().toISOString().split('T')[0]
            })
        }
      } catch (error) {
        console.error('Error saving reading session:', error)
      }
    }

    router.push(`/book/${params.id}`)
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading book...</p>
        </div>
      </div>
    )
  }

  // No book found
  if (!book) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Book not found</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            The requested book could not be found.
          </p>
          <Button onClick={() => router.push('/dashboard')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    )
  }

  // No PDF available
  if (!book.pdf_url) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No PDF available</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            This book doesn't have a PDF file associated with it.
          </p>
          <Button onClick={() => router.push(`/book/${params.id}`)}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Book Details
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <PDFReader
        pdfUrl={book.pdf_url}
        bookId={book.id}
        currentPage={currentPage}
        onPageChange={handlePageChange}
        onClose={handleClose}
      />
    </div>
  )
}
