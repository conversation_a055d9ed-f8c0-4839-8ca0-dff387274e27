@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 238 75% 59%;
    --primary-foreground: 0 0% 100%;
    --secondary: 248 100% 97%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 248 100% 97%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 248 100% 97%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 238 75% 59%;
    --radius: 0.75rem;
    --chart-1: 238 75% 59%;
    --chart-2: 248 100% 85%;
    --chart-3: 258 90% 75%;
    --chart-4: 268 85% 65%;
    --chart-5: 278 80% 70%;
    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
  }

  /* Force light theme - override dark mode completely */
  .dark {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 238 75% 59%;
    --primary-foreground: 0 0% 100%;
    --secondary: 248 100% 97%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 248 100% 97%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 248 100% 97%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 238 75% 59%;
    --chart-1: 238 75% 59%;
    --chart-2: 248 100% 85%;
    --chart-3: 258 90% 75%;
    --chart-4: 268 85% 65%;
    --chart-5: 278 80% 70%;
    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  html, body {
    @apply bg-white text-gray-900;
    background-color: white !important;
  }

  /* Force all elements to use light theme */
  *, *::before, *::after {
    color-scheme: light !important;
  }

  /* Override any potential dark backgrounds */
  .dark, [data-theme="dark"] {
    background-color: white !important;
    color: #111827 !important;
  }
}

/* Custom scrollbar - White and Violet theme */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-violet-50;
}

::-webkit-scrollbar-thumb {
  @apply bg-violet-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-violet-400;
}

/* Smooth transitions */
* {
  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
}

/* 3D Card Flip Effect */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}
