import { Book<PERSON><PERSON>, Clock, Target, TrendingUp } from "lucide-react"

export function QuickStats() {
  const stats = [
    {
      label: "Books Completed",
      value: "12",
      icon: BookOpen,
      color: "text-violet-600",
      bgColor: "bg-violet-50",
    },
    {
      label: "Pages Read",
      value: "2,847",
      icon: TrendingUp,
      color: "text-violet-600",
      bgColor: "bg-violet-100",
    },
    {
      label: "Reading Time",
      value: "127h",
      icon: Clock,
      color: "text-violet-600",
      bgColor: "bg-violet-50",
    },
    {
      label: "Current Streak",
      value: "7 days",
      icon: Target,
      color: "text-violet-600",
      bgColor: "bg-violet-100",
    },
  ]

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm border border-violet-100">
      <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Reading Stats</h2>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <div key={index} className={`${stat.bgColor} rounded-lg p-4 text-center`}>
            <div className={`inline-flex items-center justify-center w-8 h-8 ${stat.color} mb-2`}>
              <stat.icon className="w-5 h-5" />
            </div>
            <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
            <div className="text-sm text-gray-600">{stat.label}</div>
          </div>
        ))}
      </div>
    </div>
  )
}
