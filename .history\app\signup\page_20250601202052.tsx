// import { SignUp } from "@clerk/nextjs";

export default function SignUpPage() {

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-bold text-gray-900 dark:text-white">
            Join ReadingTracker
          </h2>
          <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Start your reading journey today
          </p>
          <p className="mt-4 text-sm text-blue-600 dark:text-blue-400">
            Clerk authentication will be enabled once the package installs successfully.
          </p>
        </div>
        {/* <SignUp
          appearance={{
            elements: {
              formButtonPrimary:
                "bg-indigo-600 hover:bg-indigo-700 text-sm normal-case",
            },
          }}
        /> */}
      </div>
    </div>
  );
}
