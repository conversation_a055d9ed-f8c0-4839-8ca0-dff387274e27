"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Target, TrendingUp, Clock } from "lucide-react"

export function DailyGoalWidget() {
  const [dailyGoal] = useState(30) // pages
  const [pagesReadToday] = useState(18)
  const [readingTime] = useState(45) // minutes

  const progress = (pagesReadToday / dailyGoal) * 100
  const pagesLeft = Math.max(0, dailyGoal - pagesReadToday)

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900 dark:text-white">Daily Goal</h3>
        <Target className="w-5 h-5 text-indigo-600" />
      </div>

      {/* Progress Circle or Bar */}
      <div className="space-y-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-gray-900 dark:text-white">
            {pagesReadToday}/{dailyGoal}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">pages</div>
        </div>

        <Progress value={progress} className="h-3" />

        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="flex items-center justify-center mb-1">
              <TrendingUp className="w-4 h-4 text-emerald-500 mr-1" />
            </div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">{pagesLeft}</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">pages left</div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
            <div className="flex items-center justify-center mb-1">
              <Clock className="w-4 h-4 text-blue-500 mr-1" />
            </div>
            <div className="text-lg font-semibold text-gray-900 dark:text-white">{readingTime}m</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">today</div>
          </div>
        </div>

        {progress >= 100 ? (
          <div className="text-center p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
            <div className="text-emerald-600 dark:text-emerald-400 font-medium">🎉 Goal completed!</div>
          </div>
        ) : (
          <Button className="w-full" size="sm">
            Continue Reading
          </Button>
        )}
      </div>
    </div>
  )
}
