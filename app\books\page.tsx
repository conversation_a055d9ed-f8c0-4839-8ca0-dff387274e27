import { TopNavigation } from "@/components/top-navigation"
import { BookCard } from "@/components/book-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Plus } from "lucide-react"
import Link from "next/link"

export default function BooksPage() {
  // Mock data for demonstration
  const allBooks = [
    {
      id: 1,
      title: "The Great Gatsby",
      author: "<PERSON><PERSON>",
      currentPage: 45,
      totalPages: 180,
      coverUrl: "/placeholder.svg?height=200&width=150",
      lastRead: "2024-01-15",
      status: "reading"
    },
    {
      id: 2,
      title: "To Kill a Mockingbird",
      author: "Harper Lee",
      currentPage: 120,
      totalPages: 281,
      coverUrl: "/placeholder.svg?height=200&width=150",
      lastRead: "2024-01-14",
      status: "reading"
    },
    {
      id: 3,
      title: "1984",
      author: "<PERSON>",
      currentPage: 328,
      totalPages: 328,
      coverUrl: "/placeholder.svg?height=200&width=150",
      lastRead: "2024-01-10",
      status: "completed"
    },
    {
      id: 4,
      title: "The Catcher in the Rye",
      author: "J.D. Salinger",
      currentPage: 80,
      totalPages: 224,
      coverUrl: "/placeholder.svg?height=200&width=150",
      lastRead: "2024-01-12",
      status: "reading"
    },
    {
      id: 5,
      title: "Pride and Prejudice",
      author: "Jane Austen",
      currentPage: 0,
      totalPages: 432,
      coverUrl: "/placeholder.svg?height=200&width=150",
      lastRead: null,
      status: "to-read"
    },
    {
      id: 6,
      title: "The Lord of the Rings",
      author: "J.R.R. Tolkien",
      currentPage: 1178,
      totalPages: 1178,
      coverUrl: "/placeholder.svg?height=200&width=150",
      lastRead: "2024-01-05",
      status: "completed"
    }
  ]

  const readingBooks = allBooks.filter(book => book.status === "reading")
  const completedBooks = allBooks.filter(book => book.status === "completed")
  const toReadBooks = allBooks.filter(book => book.status === "to-read")

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />
      
      <main className="container mx-auto px-4 py-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">My Library</h1>
            <p className="text-gray-600">Manage your book collection</p>
          </div>
          <Button asChild className="mt-4 sm:mt-0">
            <Link href="/add">
              <Plus className="w-4 h-4 mr-2" />
              Add Book
            </Link>
          </Button>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input 
              placeholder="Search books by title or author..." 
              className="pl-10"
            />
          </div>
          <Select>
            <SelectTrigger className="w-full sm:w-48">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Books</SelectItem>
              <SelectItem value="reading">Currently Reading</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="to-read">To Read</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Currently Reading */}
        {readingBooks.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Currently Reading ({readingBooks.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {readingBooks.map((book) => (
                <BookCard key={book.id} book={book} />
              ))}
            </div>
          </div>
        )}

        {/* To Read */}
        {toReadBooks.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Want to Read ({toReadBooks.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {toReadBooks.map((book) => (
                <BookCard key={book.id} book={book} />
              ))}
            </div>
          </div>
        )}

        {/* Completed */}
        {completedBooks.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Completed ({completedBooks.length})
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {completedBooks.map((book) => (
                <BookCard key={book.id} book={book} />
              ))}
            </div>
          </div>
        )}

        {allBooks.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No books yet</h3>
            <p className="text-gray-600 mb-4">Start building your library by adding your first book.</p>
            <Button asChild>
              <Link href="/add">Add Your First Book</Link>
            </Button>
          </div>
        )}
      </main>
    </div>
  )
}
