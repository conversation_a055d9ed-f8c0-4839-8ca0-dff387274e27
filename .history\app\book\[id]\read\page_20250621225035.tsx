"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import {
  Highlighter,
  Eraser,
  Pencil,
  StickyNote,
  BookmarkIcon,
  Sun,
  Moon,
  BookOpen,
  X,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Save,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Slider } from "@/components/ui/slider"
import { Input } from "@/components/ui/input"

export default function FullScreenReadPage({ params }: { params: { id: string } }) {
  const router = useRouter()
  const [activeTool, setActiveTool] = useState<string | null>(null)
  const [readingMode, setReadingMode] = useState<"light" | "sepia">("light")
  const [isToolbarVisible, setIsToolbarVisible] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(180)
  const [zoom, setZoom] = useState(100)
  const [isAddingNote, setIsAddingNote] = useState(false)
  const [noteText, setNoteText] = useState("")
  const [notePosition, setNotePosition] = useState({ x: 0, y: 0 })
  const pdfContainerRef = useRef<HTMLDivElement>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Auto-hide toolbar after inactivity
  useEffect(() => {
    const handleMouseMove = () => {
      setIsToolbarVisible(true)

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        if (!activeTool && !isAddingNote) {
          setIsToolbarVisible(false)
        }
      }, 3000)
    }

    window.addEventListener("mousemove", handleMouseMove)

    return () => {
      window.removeEventListener("mousemove", handleMouseMove)
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [activeTool, isAddingNote])

  // Handle tool selection
  const handleToolClick = (tool: string) => {
    if (activeTool === tool) {
      setActiveTool(null)
    } else {
      setActiveTool(tool)
      if (tool === "note") {
        setIsAddingNote(false) // Reset note adding state
      }
    }
  }

  // Handle reading mode change
  const handleReadingModeChange = (mode: "light" | "sepia") => {
    setReadingMode(mode)
  }

  // Handle page navigation
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage)
    }
  }

  // Handle PDF container click for adding notes
  const handlePdfContainerClick = (e: React.MouseEvent) => {
    if (activeTool === "note" && pdfContainerRef.current) {
      const rect = pdfContainerRef.current.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      setNotePosition({ x, y })
      setIsAddingNote(true)
    }
  }

  // Handle note save
  const handleNoteSave = () => {
    // Here you would save the note with its position and text
    console.log("Saving note:", { position: notePosition, text: noteText })
    setIsAddingNote(false)
    setNoteText("")
    setActiveTool(null)
  }

  // Get background color based on reading mode
  const getBackgroundColor = () => {
    switch (readingMode) {
      case "light":
        return "bg-white"
      case "sepia":
        return "bg-amber-50"
      default:
        return "bg-white"
    }
  }

  // Get text color based on reading mode
  const getTextColor = () => {
    switch (readingMode) {
      case "light":
        return "text-gray-900"
      case "sepia":
        return "text-amber-900"
      default:
        return "text-gray-900"
    }
  }

  return (
    <div className={`min-h-screen ${getBackgroundColor()} ${getTextColor()} flex flex-col`}>
      {/* Top Bar - only visible when toolbar is visible */}
      {isToolbarVisible && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-sm border-b border-gray-200 px-4 py-2 flex items-center justify-between">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <X className="w-5 h-5 mr-1" />
            Exit
          </Button>

          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 bg-violet-50 rounded-md px-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage <= 1}
              >
                <ChevronLeft className="w-4 h-4" />
              </Button>

              <div className="flex items-center">
                <Input
                  type="number"
                  value={currentPage}
                  onChange={(e) => handlePageChange(Number.parseInt(e.target.value) || 1)}
                  className="w-12 h-8 text-center"
                  min={1}
                  max={totalPages}
                />
                <span className="mx-1 text-sm text-gray-500">of {totalPages}</span>
              </div>

              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage >= totalPages}
              >
                <ChevronRight className="w-4 h-4" />
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">Zoom:</span>
              <div className="w-32">
                <Slider value={[zoom]} min={50} max={200} step={10} onValueChange={(value) => setZoom(value[0])} />
              </div>
              <span className="text-sm">{zoom}%</span>
            </div>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="w-5 h-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Save Progress</DropdownMenuItem>
              <DropdownMenuItem>Book Details</DropdownMenuItem>
              <DropdownMenuItem>View Notes</DropdownMenuItem>
              <DropdownMenuItem>View Highlights</DropdownMenuItem>
              <DropdownMenuItem>View Bookmarks</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}

      {/* PDF Viewer */}
      <div
        ref={pdfContainerRef}
        className={`flex-1 overflow-auto transition-colors duration-300 ${getBackgroundColor()}`}
        style={{ paddingTop: isToolbarVisible ? "60px" : "0" }}
        onClick={handlePdfContainerClick}
      >
        <div
          className="max-w-4xl mx-auto p-8"
          style={{ transform: `scale(${zoom / 100})`, transformOrigin: "top center" }}
        >
          {/* Placeholder for PDF content */}
          <div className="min-h-[calc(100vh-120px)] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg">
            <div className="text-center p-8">
              <BookOpen className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">PDF Viewer Placeholder</h2>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                In a real implementation, this would display the PDF content for page {currentPage}.
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Reading in {readingMode} mode at {zoom}% zoom
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Sticky Note Input (appears when adding a note) */}
      {isAddingNote && (
        <div
          className="absolute bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-md shadow-md w-64"
          style={{ left: `${notePosition.x}px`, top: `${notePosition.y}px` }}
        >
          <textarea
            className="w-full h-24 p-2 text-sm bg-transparent border border-yellow-300 dark:border-yellow-700 rounded focus:outline-none focus:ring-1 focus:ring-yellow-500"
            placeholder="Add your note here..."
            value={noteText}
            onChange={(e) => setNoteText(e.target.value)}
            autoFocus
          />
          <div className="flex justify-end mt-2 space-x-2">
            <Button size="sm" variant="ghost" onClick={() => setIsAddingNote(false)}>
              Cancel
            </Button>
            <Button size="sm" onClick={handleNoteSave}>
              <Save className="w-4 h-4 mr-1" />
              Save
            </Button>
          </div>
        </div>
      )}

      {/* Floating Toolbar */}
      {isToolbarVisible && (
        <TooltipProvider>
          <div className="fixed bottom-6 left-6 z-50">
            <div className="bg-white dark:bg-gray-800 rounded-full shadow-lg border border-gray-200 dark:border-gray-700 p-2 flex flex-col space-y-2">
              {/* Highlighter Tool */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={activeTool === "highlighter" ? "default" : "ghost"}
                    size="icon"
                    className="h-10 w-10 rounded-full"
                    onClick={() => handleToolClick("highlighter")}
                  >
                    <Highlighter className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Highlighter</p>
                </TooltipContent>
              </Tooltip>

              {/* Pen/Marker Tool */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={activeTool === "pen" ? "default" : "ghost"}
                    size="icon"
                    className="h-10 w-10 rounded-full"
                    onClick={() => handleToolClick("pen")}
                  >
                    <Pencil className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Pen/Marker</p>
                </TooltipContent>
              </Tooltip>

              {/* Eraser Tool */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={activeTool === "eraser" ? "default" : "ghost"}
                    size="icon"
                    className="h-10 w-10 rounded-full"
                    onClick={() => handleToolClick("eraser")}
                  >
                    <Eraser className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Eraser</p>
                </TooltipContent>
              </Tooltip>

              {/* Sticky Note Tool */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={activeTool === "note" ? "default" : "ghost"}
                    size="icon"
                    className="h-10 w-10 rounded-full"
                    onClick={() => handleToolClick("note")}
                  >
                    <StickyNote className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Add Note</p>
                </TooltipContent>
              </Tooltip>

              {/* Bookmark Tool */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-10 w-10 rounded-full"
                    onClick={() => console.log("Bookmark page", currentPage)}
                  >
                    <BookmarkIcon className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Bookmark Page</p>
                </TooltipContent>
              </Tooltip>

              {/* Reading Mode Separator */}
              <div className="border-t border-gray-200 dark:border-gray-700 my-1 w-full"></div>

              {/* Light Mode */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={readingMode === "light" ? "default" : "ghost"}
                    size="icon"
                    className="h-10 w-10 rounded-full"
                    onClick={() => handleReadingModeChange("light")}
                  >
                    <Sun className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Light Mode</p>
                </TooltipContent>
              </Tooltip>

              {/* Dark Mode */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={readingMode === "dark" ? "default" : "ghost"}
                    size="icon"
                    className="h-10 w-10 rounded-full"
                    onClick={() => handleReadingModeChange("dark")}
                  >
                    <Moon className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Dark Mode</p>
                </TooltipContent>
              </Tooltip>

              {/* Sepia Mode */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant={readingMode === "sepia" ? "default" : "ghost"}
                    size="icon"
                    className="h-10 w-10 rounded-full"
                    onClick={() => handleReadingModeChange("sepia")}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M12 3v12" />
                      <path d="M6 15h12" />
                      <path d="M3 9h18" />
                    </svg>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">
                  <p>Sepia Mode</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>
        </TooltipProvider>
      )}
    </div>
  )
}
