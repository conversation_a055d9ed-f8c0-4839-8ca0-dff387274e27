import { TopNavigation } from "@/components/top-navigation"
import { BookCard } from "@/components/book-card"
import { DailyGoalWidget } from "@/components/daily-goal-widget"
import { StreakCalendar } from "@/components/streak-calendar"
import { AddBookFAB } from "@/components/add-book-fab"
import { RecentActivity } from "@/components/recent-activity"
import { Button } from "@/components/ui/button"
import { BookOpen, TrendingUp, Clock, Target } from "lucide-react"
import Link from "next/link"

// Mock data for demonstration
const currentBooks = [
  {
    id: 1,
    title: "The Great Gatsby",
    author: "<PERSON><PERSON>",
    currentPage: 45,
    totalPages: 180,
    coverUrl: "/placeholder.svg?height=200&width=150",
    lastRead: "2024-01-15",
  },
  {
    id: 2,
    title: "To Kill a Mockingbird",
    author: "<PERSON> Lee",
    currentPage: 120,
    totalPages: 281,
    coverUrl: "/placeholder.svg?height=200&width=150",
    lastRead: "2024-01-14",
  },
  {
    id: 3,
    title: "1984",
    author: "<PERSON>",
    currentPage: 200,
    totalPages: 328,
    coverUrl: "/placeholder.svg?height=200&width=150",
    lastRead: "2024-01-13",
  },
  {
    id: 4,
    title: "The Catcher in the Rye",
    author: "J.D. Salinger",
    currentPage: 80,
    totalPages: 224,
    coverUrl: "/placeholder.svg?height=200&width=150",
    lastRead: "2024-01-12",
  },
]

export default function DashboardPage() {
  // Stats data
  const stats = [
    {
      label: "Books Completed",
      value: "12",
      icon: BookOpen,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      label: "Pages Read",
      value: "2,847",
      icon: TrendingUp,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
    },
    {
      label: "Reading Time",
      value: "127h",
      icon: Clock,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      label: "Current Streak",
      value: "7 days",
      icon: Target,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <TopNavigation />

      <main className="container mx-auto px-4 py-6">
        {/* Stats Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {stats.map((stat, index) => (
            <div
              key={index}
              className={`${stat.bgColor} rounded-xl p-4 flex items-center space-x-4 shadow-sm border border-gray-100`}
            >
              <div className={`rounded-full p-3 ${stat.bgColor}`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-900">{stat.value}</div>
                <div className="text-sm text-gray-600">{stat.label}</div>
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Welcome Section */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">Welcome back! 📚</h1>
              <p className="text-gray-600">
                Continue your reading journey and reach your daily goals.
              </p>
              <div className="flex space-x-4 mt-4">
                <Button asChild>
                  <Link href="/add">Add New Book</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/stats">View Stats</Link>
                </Button>
              </div>
            </div>

            {/* Current Books */}
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Continue Reading</h2>
                <Link href="/books" className="text-sm text-indigo-600 hover:underline">
                  View all books
                </Link>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {currentBooks.map((book) => (
                  <BookCard key={book.id} book={book} />
                ))}
              </div>
            </div>

            {/* Recent Activity */}
            <RecentActivity />
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Daily Goal Widget */}
            <DailyGoalWidget />

            {/* Reading Streak Calendar */}
            <StreakCalendar />
          </div>
        </div>
      </main>

      {/* Floating Add Book Button */}
      <AddBookFAB />
    </div>
  )
}
